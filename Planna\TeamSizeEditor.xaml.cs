using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Planna;

public partial class TeamSizeEditor : UserControl
{
    private bool _isUpdatingTextBox = false;

    public static readonly DependencyProperty ValueProperty =
        DependencyProperty.Register("Value", typeof(int), typeof(TeamSizeEditor),
            new FrameworkPropertyMetadata(0, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnValueChanged));

    public int Value
    {
        get { return (int)GetValue(ValueProperty); }
        set { SetValue(ValueProperty, value); }
    }

    public static readonly DependencyProperty MinimumProperty =
        DependencyProperty.Register("Minimum", typeof(int), typeof(TeamSizeEditor),
            new PropertyMetadata(0));

    public int Minimum
    {
        get { return (int)GetValue(MinimumProperty); }
        set { SetValue(MinimumProperty, value); }
    }

    public static readonly DependencyProperty MaximumProperty =
        DependencyProperty.Register("Maximum", typeof(int), typeof(TeamSizeEditor),
            new PropertyMetadata(100));

    public int Maximum
    {
        get { return (int)GetValue(MaximumProperty); }
        set { SetValue(MaximumProperty, value); }
    }

    public TeamSizeEditor()
    {
        InitializeComponent();
        ValueTextBox.TextChanged += ValueTextBox_TextChanged;
        ValueTextBox.LostFocus += ValueTextBox_LostFocus;
        ValueTextBox.KeyDown += ValueTextBox_KeyDown;
    }

    private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TeamSizeEditor editor && !editor._isUpdatingTextBox)
        {
            editor._isUpdatingTextBox = true;
            editor.ValueTextBox.Text = e.NewValue.ToString();
            editor._isUpdatingTextBox = false;
        }
    }

    private void ValueTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (_isUpdatingTextBox) return;

        if (int.TryParse(ValueTextBox.Text, out int newValue))
        {
            newValue = Math.Max(Minimum, Math.Min(Maximum, newValue));
            if (newValue != Value)
            {
                Value = newValue;
            }
        }
    }

    private void ValueTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        // Ensure the text box shows the current valid value
        if (!_isUpdatingTextBox)
        {
            _isUpdatingTextBox = true;
            ValueTextBox.Text = Value.ToString();
            _isUpdatingTextBox = false;
        }
    }

    private void ValueTextBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            ValueTextBox_LostFocus(sender, e);
            Keyboard.ClearFocus(); // Remove focus from the textbox
        }
    }

    private void IncrementButton_Click(object sender, RoutedEventArgs e)
    {
        if (Value < Maximum)
        {
            Value++;
        }
    }

    private void DecrementButton_Click(object sender, RoutedEventArgs e)
    {
        if (Value > Minimum)
        {
            Value--;
        }
    }
}
