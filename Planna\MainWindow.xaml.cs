﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using OxyPlot;
using OxyPlot.Series;
using OxyPlot.Axes;

namespace Planna;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window, INotifyPropertyChanged
{
    public ObservableCollection<MonthlyPlanData> MonthlyData { get; set; }
    public BusinessLogic BusinessLogic { get; set; }
    public GrowthProjectionEngine ProjectionEngine { get; set; }
    public List<GrowthProjection> CurrentProjections { get; set; }

    private GrowthProjection _selectedMonthProjection = new GrowthProjection();
    public GrowthProjection SelectedMonthProjection
    {
        get => _selectedMonthProjection;
        set
        {
            _selectedMonthProjection = value;
            OnPropertyChanged();
        }
    }

    public MainWindow()
    {
        InitializeComponent();
        BusinessLogic = new BusinessLogic();
        ProjectionEngine = new GrowthProjectionEngine(BusinessLogic);
        InitializeData();
        SetupDataBinding();
        UpdateProjections();
    }

    private void InitializeData()
    {
        MonthlyData = new ObservableCollection<MonthlyPlanData>
        {
            new MonthlyPlanData(BusinessLogic) { Month = "Aug '25", SalesReps = 1, MarketingTeam = 0, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 2000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Sep '25", SalesReps = 2, MarketingTeam = 1, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 3500 },
            new MonthlyPlanData(BusinessLogic) { Month = "Oct '25", SalesReps = 3, MarketingTeam = 1, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 2500 },
            new MonthlyPlanData(BusinessLogic) { Month = "Nov '25", SalesReps = 4, MarketingTeam = 2, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 5000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Dec '25", SalesReps = 5, MarketingTeam = 3, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 8000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Jan '26", SalesReps = 5, MarketingTeam = 4, DevTeam = 0, OpsTeam = 1, LeadershipTeam = 0, EventsCost = 10000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Feb '26", SalesReps = 5, MarketingTeam = 4, DevTeam = 0, OpsTeam = 1, LeadershipTeam = 0, EventsCost = 5000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Mar '26", SalesReps = 6, MarketingTeam = 4, DevTeam = 1, OpsTeam = 1, LeadershipTeam = 0, EventsCost = 7000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Apr '26", SalesReps = 7, MarketingTeam = 5, DevTeam = 1, OpsTeam = 1, LeadershipTeam = 1, EventsCost = 6000 },
            new MonthlyPlanData(BusinessLogic) { Month = "May '26", SalesReps = 8, MarketingTeam = 5, DevTeam = 2, OpsTeam = 2, LeadershipTeam = 1, EventsCost = 8000 }
        };

        MonthlyPlanningGrid.ItemsSource = MonthlyData;
    }

    private void SetupDataBinding()
    {
        // Set DataContext for the business assumptions panel
        this.DataContext = BusinessLogic;

        // Subscribe to business logic changes to update calculations
        BusinessLogic.PropertyChanged += (s, e) => UpdateCalculations();

        // Subscribe to monthly data changes
        foreach (var monthData in MonthlyData)
        {
            monthData.PropertyChanged += (s, e) => UpdateCalculations();
        }
    }

    private void UpdateCalculations()
    {
        UpdateProjections();
        CommandManager.InvalidateRequerySuggested();
    }

    private void UpdateProjections()
    {
        CurrentProjections = ProjectionEngine.CalculateProjections(MonthlyData);

        // Set the selected month to the latest month for unit economics display
        SelectedMonthProjection = CurrentProjections.LastOrDefault() ?? new GrowthProjection();

        SetupChart();
        UpdateUnitEconomicsDisplay();
    }

    private void UpdateUnitEconomicsDisplay()
    {
        // The SelectedMonthProjection property setter now handles PropertyChanged notification
        // No additional action needed here since the binding will update automatically
    }

    private void SetupChart()
    {
        if (CurrentProjections == null || !CurrentProjections.Any())
            return;

        var plotModel = new PlotModel
        {
            Title = "Growth Metrics Over Time",
            Background = OxyColors.White,
            PlotAreaBorderColor = OxyColors.LightGray,
            PlotAreaBorderThickness = new OxyThickness(1)
        };

        // Add axes with better formatting
        var categoryAxis = new CategoryAxis
        {
            Position = AxisPosition.Bottom,
            Title = "Month",
            ItemsSource = CurrentProjections.Select(p => p.Month).ToArray(),
            Angle = -45,
            FontSize = 10
        };
        plotModel.Axes.Add(categoryAxis);

        var valueAxis = new LinearAxis
        {
            Position = AxisPosition.Left,
            Title = "Value",
            Minimum = 0,
            StringFormat = "N0",
            MajorGridlineStyle = LineStyle.Solid,
            MajorGridlineColor = OxyColors.LightGray,
            MinorGridlineStyle = LineStyle.Dot,
            MinorGridlineColor = OxyColors.LightGray
        };
        plotModel.Axes.Add(valueAxis);

        // Add series based on real data
        AddChartSeries(plotModel, "MRR ($)", CurrentProjections.Select(p => (double)p.MRR).ToArray(), OxyColors.Blue, true);
        AddChartSeries(plotModel, "Customers", CurrentProjections.Select(p => (double)p.TotalCustomers).ToArray(), OxyColors.Green, true);
        AddChartSeries(plotModel, "Cash on Hand ($)", CurrentProjections.Select(p => (double)p.CashOnHand).ToArray(), OxyColors.Orange, false);
        AddChartSeries(plotModel, "Monthly Burn ($)", CurrentProjections.Select(p => (double)p.MonthlyBurn).ToArray(), OxyColors.Red, false);
        AddChartSeries(plotModel, "Team Size", CurrentProjections.Select(p => (double)(p.SalesReps + p.MarketingTeam + p.DevTeam + p.OpsTeam + p.LeadershipTeam)).ToArray(), OxyColors.Purple, false);
        AddChartSeries(plotModel, "Runway (Months)", CurrentProjections.Select(p => (double)Math.Min(p.RunwayMonths, 100)).ToArray(), OxyColors.Brown, false);

        // Add click interaction to chart
        GrowthChart.MouseDown += (s, e) =>
        {
            if (e.LeftButton == System.Windows.Input.MouseButtonState.Pressed && CurrentProjections?.Any() == true)
            {
                // Simple click interaction - cycle through months
                var currentIndex = CurrentProjections.IndexOf(SelectedMonthProjection);
                var nextIndex = (currentIndex + 1) % CurrentProjections.Count;
                SelectedMonthProjection = CurrentProjections[nextIndex];
                // Property setter now handles the UI update automatically
            }
        };

        // Enable zooming and panning with better UX
        plotModel.Axes.ToList().ForEach(axis =>
        {
            axis.IsZoomEnabled = true;
            axis.IsPanEnabled = true;
        });

        GrowthChart.Model = plotModel;
        GrowthChart.InvalidatePlot(true);
    }

    private void AddChartSeries(PlotModel plotModel, string title, double[] data, OxyColor color, bool isVisible)
    {
        var series = new LineSeries
        {
            Title = title,
            Color = color,
            StrokeThickness = 3,
            MarkerType = MarkerType.Circle,
            MarkerSize = 5,
            MarkerFill = color,
            MarkerStroke = OxyColors.White,
            MarkerStrokeThickness = 1,
            IsVisible = isVisible
        };

        for (int i = 0; i < data.Length; i++)
        {
            series.Points.Add(new DataPoint(i, data[i]));
        }

        plotModel.Series.Add(series);
    }

    private void MetricToggle_Changed(object sender, RoutedEventArgs e)
    {
        if (GrowthChart?.Model != null)
        {
            var plotModel = GrowthChart.Model;

            // Update series visibility based on toggle states
            foreach (var series in plotModel.Series.OfType<LineSeries>())
            {
                series.IsVisible = series.Title switch
                {
                    "MRR ($)" => MRRToggle?.IsChecked == true,
                    "Customers" => CustomersToggle?.IsChecked == true,
                    "Cash on Hand ($)" => CashToggle?.IsChecked == true,
                    "Monthly Burn ($)" => BurnToggle?.IsChecked == true,
                    "Team Size" => TeamToggle?.IsChecked == true,
                    "Runway (Months)" => RunwayToggle?.IsChecked == true,
                    _ => series.IsVisible
                };
            }

            // Refresh the chart
            GrowthChart.InvalidatePlot(true);
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}